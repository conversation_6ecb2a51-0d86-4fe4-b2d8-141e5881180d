/**
 * User Data Adapter
 * Transforms user and authentication data between API and frontend formats
 */

import { BaseAdapter, AdapterRegistry } from './BaseAdapter.js'

export class UserAdapter extends BaseAdapter {
  /**
   * Transform user data from API to frontend format
   * @param {Object} user - User data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed user data
   */
  static transformItem(user, type = 'default') {
    if (!user) return null
    
    const base = super.transformItem(user, type)
    
    switch (type) {
      case 'profile':
        return {
          id: user.id || user.user_id,
          username: user.username,
          email: user.email,
          name: user.name || user.full_name,
          firstName: user.first_name || user.name?.split(' ')[0],
          lastName: user.last_name || user.name?.split(' ').slice(1).join(' '),
          avatar: user.avatar || user.profile_picture,
          role: user.role || 'user',
          status: user.status || 'active',
          // Enhanced profile fields
          displayName: user.name || user.username,
          displayRole: this.formatRole(user.role),
          displayStatus: this.formatStatus(user.status),
          // Profile details
          bio: user.bio || user.description,
          location: user.location,
          website: user.website,
          phone: user.phone,
          // University info
          universityId: user.university_id,
          universityName: user.university_name,
          majorId: user.major_id,
          majorName: user.major_name,
          // Dates
          lastLoginAt: user.last_login_at || user.lastLoginAt,
          joinedAt: user.created_at || user.joinedAt,
          ...base
        }
      
      case 'list':
        return {
          id: user.id || user.user_id,
          username: user.username,
          email: user.email,
          name: user.name || user.full_name,
          avatar: user.avatar || user.profile_picture,
          role: user.role || 'user',
          status: user.status || 'active',
          // Display fields
          displayName: user.name || user.username,
          displayRole: this.formatRole(user.role),
          displayStatus: this.formatStatus(user.status),
          // University info
          universityName: user.university_name,
          majorName: user.major_name,
          // Stats
          totalCredits: user.total_credits || 0,
          totalCompetitions: user.total_competitions || 0,
          lastActive: user.last_login_at || user.lastLoginAt,
          ...base
        }
      
      case 'ranking':
        return {
          id: user.id || user.user_id,
          username: user.username,
          name: user.name || user.full_name,
          avatar: user.avatar || user.profile_picture,
          universityName: user.university_name,
          majorName: user.major_name,
          // Ranking specific fields
          rank: user.rank || 0,
          totalCredits: user.total_credits || 0,
          routeCredits: user.route_credits || {},
          displayName: user.name || user.username,
          displayCredits: `${user.total_credits || 0} credits`,
          displayRank: this.formatRank(user.rank)
        }
      
      case 'dropdown':
        return {
          value: user.id || user.user_id,
          label: user.name || user.username,
          email: user.email,
          role: user.role
        }
      
      default:
        return {
          id: user.id || user.user_id,
          username: user.username,
          email: user.email,
          name: user.name || user.full_name,
          avatar: user.avatar || user.profile_picture,
          role: user.role || 'user',
          status: user.status || 'active',
          ...base
        }
    }
  }
  
  /**
   * Transform authentication response
   * @param {Object} authData - Authentication response data
   * @returns {Object} Transformed auth data
   */
  static transformAuthResponse(authData) {
    if (!authData) return null

    return {
      token: authData.token,
      refreshToken: authData.refresh_token,
      expiresIn: authData.expires_in,
      tokenType: authData.token_type || 'Bearer',
      user: this.transformItem(authData.user, 'profile'),
      permissions: authData.permissions || [],
      roles: authData.roles || [authData.user?.role || 'user']
    }
  }
  
  /**
   * Transform login request
   * @param {Object} credentials - Login credentials
   * @returns {Object} Transformed login request
   */
  static transformLoginRequest(credentials) {
    return {
      username: credentials.username || credentials.email,
      email: credentials.email,
      password: credentials.password
    }
  }
  
  /**
   * Transform registration request
   * @param {Object} userData - Registration data
   * @returns {Object} Transformed registration request
   */
  static transformRegistrationRequest(userData) {
    console.log('User data before request is made',userData)
    return {
      username: userData.username || userData.name,
      email: userData.email,
      password: userData.password,
      confirm_password: userData.confirmPassword,
      first_name: userData.firstName,
      last_name: userData.lastName,
      university_id: userData.universityId,
      major_id: userData.majorId,
      phone: userData.phone,
      bio: userData.bio
    }
  }
  
  /**
   * Format user role for display
   * @param {string} role - User role
   * @returns {string} Formatted role
   */
  static formatRole(role) {
    // Simplified for admin-only system
    const roleMap = {
      'admin': 'Administrator',
      'super_admin': 'Super Administrator'
    }

    return roleMap[role] || 'Administrator'
  }
  
  /**
   * Format user status for display
   * @param {string} status - User status
   * @returns {string} Formatted status
   */
  static formatStatus(status) {
    const statusMap = {
      'active': 'Active',
      'inactive': 'Inactive',
      'suspended': 'Suspended',
      'banned': 'Banned',
      'pending': 'Pending Verification'
    }
    
    return statusMap[status] || 'Unknown'
  }
  
  /**
   * Format rank for display
   * @param {number} rank - User rank
   * @returns {string} Formatted rank
   */
  static formatRank(rank) {
    if (!rank || rank === 0) return 'Unranked'
    
    const suffix = this.getOrdinalSuffix(rank)
    return `${rank}${suffix}`
  }
  
  /**
   * Get ordinal suffix for numbers
   * @param {number} num - Number
   * @returns {string} Ordinal suffix
   */
  static getOrdinalSuffix(num) {
    const j = num % 10
    const k = num % 100
    
    if (j === 1 && k !== 11) return 'st'
    if (j === 2 && k !== 12) return 'nd'
    if (j === 3 && k !== 13) return 'rd'
    return 'th'
  }
  
  /**
   * Transform user list response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(response, options = {}) {
    const { type = 'list', isPaginated = true } = options
    
    if (isPaginated) {
      return this.transformPaginated(response, type)
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformItem(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformItem(response.data, type)
    }
  }
  
  /**
   * Transform user rankings response
   * @param {Object} response - Rankings response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transformRankings(response, options = {}) {
    const { type = 'ranking' } = options
    
    if (response.data?.res) {
      // Handle paginated rankings
      return {
        ...response,
        data: {
          ...response.data,
          res: response.data.res.map((item, index) => ({
            ...this.transformItem(item, type),
            rank: (response.data.current_page - 1) * response.data.page_size + index + 1
          }))
        }
      }
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map((item, index) => ({
          ...this.transformItem(item, type),
          rank: index + 1
        }))
      }
    }
    
    return response
  }
}

// Register the adapter
AdapterRegistry.register('user', UserAdapter)
AdapterRegistry.register('auth', UserAdapter)

export { UserAdapter as userAdapter }
