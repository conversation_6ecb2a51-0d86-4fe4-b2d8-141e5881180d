/**
 * Authentication API Service
 * Handles user authentication, registration, and session management
 * Note: Auth endpoints are not documented yet, using standard patterns
 */

import { api } from '../api.js'
import { handleApiResponse, handleApiError, logApiCall } from '../../utils/apiHelpers.js'

const BASE_PATH = '/camp-admin/auth'

/**
 * User login
 * @param {Object} credentials - Login credentials
 * @param {string} credentials.username - Username or email
 * @param {string} credentials.password - Password
 * @returns {Promise<Object>} Login response with token and user data
 */
export const login = async (credentials) => {
  try {
    const url = `${BASE_PATH}/login`
    
    logApiCall('POST', url, { username: credentials.username }, null)
    
    const response = await api.post(url, credentials)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, { username: credentials.username }, { success: result.success })
    
    return {
      success: true,
      data: {
        token: result.data.token,
        user: result.data.user
      },
      message: result.data.message
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/login`, { username: credentials.username }, normalizedError)
    throw normalizedError
  }
}

/**
 * User registration - DISABLED for admin-only system
 * @returns {Promise<Object>} Registration disabled error
 */
export const register = async () => {
  throw new Error('Registration is disabled. This is an admin-only system.')
}

/**
 * Get current user profile
 * @returns {Promise<Object>} Current user data
 */
export const getCurrentUser = async () => {
  try {
    const url = `${BASE_PATH}/me`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, { success: result.success })
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/me`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * User logout
 * @returns {Promise<Object>} Logout response
 */
export const logout = async () => {
  try {
    const url = `${BASE_PATH}/logout`
    
    logApiCall('POST', url, null, null)
    
    const response = await api.post(url)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, null, { success: result.success })
    
    return {
      success: true,
      message: result.data.message
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/logout`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Refresh authentication token
 * @param {string} refreshToken - Refresh token
 * @returns {Promise<Object>} New token response
 */
export const refreshToken = async (refreshToken) => {
  try {
    const url = `${BASE_PATH}/refresh`
    
    logApiCall('POST', url, null, null)
    
    const response = await api.post(url, { refresh_token: refreshToken })
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, null, { success: result.success })
    
    return {
      success: true,
      data: {
        token: result.data.token,
        refreshToken: result.data.refresh_token
      }
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/refresh`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Change password
 * @param {Object} passwordData - Password change data
 * @param {string} passwordData.current_password - Current password
 * @param {string} passwordData.new_password - New password
 * @returns {Promise<Object>} Password change response
 */
export const changePassword = async (passwordData) => {
  try {
    const url = `${BASE_PATH}/change-password`
    
    logApiCall('POST', url, null, null)
    
    const response = await api.post(url, passwordData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, null, { success: result.success })
    
    return {
      success: true,
      message: result.data.message
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/change-password`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Request password reset
 * @param {Object} resetData - Password reset request data
 * @param {string} resetData.email - Email address
 * @returns {Promise<Object>} Password reset request response
 */
export const requestPasswordReset = async (resetData) => {
  try {
    const url = `${BASE_PATH}/forgot-password`
    
    logApiCall('POST', url, { email: resetData.email }, null)
    
    const response = await api.post(url, resetData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, { email: resetData.email }, { success: result.success })
    
    return {
      success: true,
      message: result.data.message
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/forgot-password`, { email: resetData.email }, normalizedError)
    throw normalizedError
  }
}

/**
 * Reset password with token
 * @param {Object} resetData - Password reset data
 * @param {string} resetData.token - Reset token
 * @param {string} resetData.new_password - New password
 * @returns {Promise<Object>} Password reset response
 */
export const resetPassword = async (resetData) => {
  try {
    const url = `${BASE_PATH}/reset-password`
    
    logApiCall('POST', url, null, null)
    
    const response = await api.post(url, resetData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, null, { success: result.success })
    
    return {
      success: true,
      message: result.data.message
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/reset-password`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Verify authentication token
 * @param {string} token - Authentication token
 * @returns {Promise<Object>} Token verification response
 */
export const verifyToken = async (token) => {
  try {
    const url = `${BASE_PATH}/verify`

    logApiCall('GET', url, null, null)

    // Send token in Authorization header as expected by backend
    const response = await api.get(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    const result = handleApiResponse(response)

    logApiCall('GET', url, null, { success: result.success })

    return {
      success: true,
      data: result.data,
      valid: result.data.valid || false
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/verify`, null, normalizedError)
    throw normalizedError
  }
}

// Export all functions as a service object
export const authApi = {
  login,
  register,
  getCurrentUser,
  logout,
  refreshToken,
  changePassword,
  requestPasswordReset,
  resetPassword,
  verifyToken
}
